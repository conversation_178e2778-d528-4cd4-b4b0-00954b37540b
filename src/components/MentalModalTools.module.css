.customButtons {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid var(--ifm-color-emphasis-200);
  position: sticky;
  bottom: 0;
  background: var(--ifm-background-color);
  z-index: 1;
}

.buttonGroup {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.button {
  display: flex;
  align-items: center;
  padding: 0.5rem 0.75rem;
  background: var(--ifm-color-emphasis-100);
  border: 1px solid var(--ifm-color-emphasis-300);
  border-radius: 0.375rem;
  color: var(--ifm-color-content);
  text-decoration: none;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.button:hover {
  background: var(--ifm-color-emphasis-200);
  border-color: var(--ifm-color-emphasis-400);
  text-decoration: none;
}

/* 响应式处理 */
@media (max-width: 996px) {
  .customButtonsFixed {
    display: none; /* 在小屏幕上隐藏 */
  }
}