.customButtons {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid var(--ifm-color-emphasis-200);
  position: sticky;
  top: calc(var(--ifm-navbar-height) + 1rem);
  background: var(--ifm-background-color);
  z-index: 10;
  max-height: calc(100vh - var(--ifm-navbar-height) - 2rem);
  overflow-y: auto;
}

.buttonGroup {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.button {
  display: flex;
  align-items: center;
  padding: 0.5rem 0.75rem;
  background: var(--ifm-color-emphasis-100);
  border: 1px solid var(--ifm-color-emphasis-300);
  border-radius: 0.375rem;
  color: var(--ifm-color-content);
  text-decoration: none;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.button:hover {
  background: var(--ifm-color-emphasis-200);
  border-color: var(--ifm-color-emphasis-400);
  text-decoration: none;
}

/* 分享容器 */
.shareContainer {
  position: relative;
}

/* 分享菜单 */
.shareMenu {
  position: absolute;
  bottom: 100%;
  left: 0;
  right: 0;
  background: var(--ifm-background-color);
  border: 1px solid var(--ifm-color-emphasis-300);
  border-radius: 0.375rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  margin-bottom: 0.5rem;
  z-index: 1000;
  overflow: hidden;
}

.shareOption {
  display: flex;
  align-items: center;
  padding: 0.75rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
  border-bottom: 1px solid var(--ifm-color-emphasis-200);
  font-size: 0.875rem;
}

.shareOption:last-child {
  border-bottom: none;
}

.shareOption:hover {
  background: var(--ifm-color-emphasis-100);
}

.shareIcon {
  margin-right: 0.75rem;
  font-size: 1rem;
}

/* 响应式处理 */
@media (max-width: 996px) {
  .customButtons {
    position: relative;
    top: auto;
    max-height: none;
    overflow-y: visible;
  }

  .customButtonsFixed {
    display: none; /* 在小屏幕上隐藏 */
  }
}