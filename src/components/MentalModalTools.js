import React from 'react';
import styles from './MentalModalTools.module.css';
import { useLocation } from '@docusaurus/router';
import { useDoc, useSidebarBreadcrumbs } from '@docusaurus/plugin-content-docs/client';

export default function MentalModalTools() {
  const { metadata, content } = useDoc();
  const breadcrumbs = useSidebarBreadcrumbs();
  const location = useLocation();

  const getDomainName = () => {
    if (typeof window === 'undefined') return ''; // SSR 安全检查

    const hostname = window.location.hostname;
    // 移除 www 前缀
    return hostname.replace(/^www\./, '');
  };

  const domainName = getDomainName();
  // console.log('bread crumbs.................', content)
  // 检查当前文档是否属于目标目录
  const isMentalModalPage = metadata.permalink.startsWith('/thinking-matters/classic-mental-models/');
  // const shouldShowButtons = true;

  const handleShare = () => {
    // 处理分享逻辑
    navigator.share({
      title: document.title,
      url: window.location.href,
    });
  };

  return (
    <div className={styles.customButtons}>
      <div className={styles.buttonGroup}>
        {
          isMentalModalPage && (
            <a
              className={styles.button}
              href={`/aitools/mindkit?mental_model=${breadcrumbs.find(item => item.type === 'link')?.label}`}
              target='_blank'
            >
              {`Apply '${breadcrumbs.find(item => item.type === 'link')?.label}' with MindKit`}
            </a>
          )
        }
        <a
          className={styles.button}
          href={`https://app.${domainName}/#/aiflow?queryType=link&url=${encodeURIComponent(window.location.href)}`}
          target='_blank'
        >
          Read with FunBlocks Mindmap
        </a>
        <button className={styles.button} onClick={handleEdit}>
          ✏️ 编辑
        </button>
        <button className={styles.button} onClick={handleShare}>
          🔗 Share
        </button>
      </div>
    </div>
  );
}