import { useState, useEffect } from 'react';
import styles from './MentalModalTools.module.css';
import { useDoc, useSidebarBreadcrumbs } from '@docusaurus/plugin-content-docs/client';

export default function MentalModalTools() {
  const { metadata } = useDoc();
  const breadcrumbs = useSidebarBreadcrumbs();
  const [showShareMenu, setShowShareMenu] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    // 检测是否为移动设备
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768 || /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent));
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);

    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  useEffect(() => {
    // 点击外部关闭分享菜单
    const handleClickOutside = (event) => {
      if (showShareMenu && !event.target.closest(`.${styles.shareContainer}`)) {
        setShowShareMenu(false);
      }
    };

    document.addEventListener('click', handleClickOutside);
    return () => document.removeEventListener('click', handleClickOutside);
  }, [showShareMenu]);

  const getDomainName = () => {
    if (typeof window === 'undefined') return ''; // SSR 安全检查

    const hostname = window.location.hostname;
    // 移除 www 前缀
    return hostname.replace(/^www\./, '');
  };

  const domainName = getDomainName();
  // 检查当前文档是否属于目标目录
  const isMentalModalPage = metadata.permalink.startsWith('/thinking-matters/classic-mental-models/');

  const handleShare = () => {
    if (isMobile && navigator.share) {
      // 移动端使用系统分享
      navigator.share({
        title: document.title,
        url: window.location.href,
      }).catch(err => console.log('Error sharing:', err));
    } else {
      // 桌面端显示分享菜单
      setShowShareMenu(!showShareMenu);
    }
  };

  const shareToSocial = (platform) => {
    const url = encodeURIComponent(window.location.href);
    const title = encodeURIComponent(document.title);

    const shareUrls = {
      twitter: `https://twitter.com/intent/tweet?url=${url}&text=${title}`,
      facebook: `https://www.facebook.com/sharer/sharer.php?u=${url}`,
      linkedin: `https://www.linkedin.com/sharing/share-offsite/?url=${url}`,
      reddit: `https://reddit.com/submit?url=${url}&title=${title}`,
      email: `mailto:?subject=${title}&body=${url}`,
      whatsapp: `https://wa.me/?text=${title}%20${url}`
    };

    if (platform === 'email') {
      window.location.href = shareUrls[platform];
    } else {
      window.open(shareUrls[platform], '_blank', 'width=600,height=400');
    }

    setShowShareMenu(false);
  };

  return (
    <div className={styles.customButtons}>
      <div className={styles.buttonGroup}>
        {
          isMentalModalPage && (
            <a
              className={styles.button}
              href={`/aitools/mindkit?mental_model=${breadcrumbs.find(item => item.type === 'link')?.label}`}
              target='_blank'
            >
              {`Apply '${breadcrumbs.find(item => item.type === 'link')?.label}' with MindKit`}
            </a>
          )
        }
        <a
          className={styles.button}
          href={`https://app.${domainName}/#/aiflow?queryType=link&url=${encodeURIComponent(window.location.href)}`}
          target='_blank'
        >
          Read with FunBlocks Mindmap
        </a>
        <div className={styles.shareContainer}>
          <div className={styles.button} onClick={handleShare}>
            🔗 Share
          </div>
          {showShareMenu && !isMobile && (
            <div className={styles.shareMenu}>
              <div className={styles.shareOption} onClick={() => shareToSocial('twitter')}>
                <span className={styles.shareIcon}>🐦</span>
                Twitter
              </div>
              <div className={styles.shareOption} onClick={() => shareToSocial('facebook')}>
                <span className={styles.shareIcon}>📘</span>
                Facebook
              </div>
              <div className={styles.shareOption} onClick={() => shareToSocial('linkedin')}>
                <span className={styles.shareIcon}>💼</span>
                LinkedIn
              </div>
              <div className={styles.shareOption} onClick={() => shareToSocial('reddit')}>
                <span className={styles.shareIcon}>🔴</span>
                Reddit
              </div>
              <div className={styles.shareOption} onClick={() => shareToSocial('whatsapp')}>
                <span className={styles.shareIcon}>💬</span>
                WhatsApp
              </div>
              <div className={styles.shareOption} onClick={() => shareToSocial('email')}>
                <span className={styles.shareIcon}>📧</span>
                Email
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}